# FluentToast Component

The `FluentToast` component provides elegant, non-intrusive notifications that appear temporarily to inform users about actions, status changes, or important information. It implements Microsoft's Fluent Design principles with smooth animations, flexible positioning, and comprehensive customization options.

## Overview

FluentToast offers:
- **Multiple toast types** (Info, Success, Warning, Error, Custom)
- **Flexible positioning** (Top-left, Top-center, Top-right, Bottom-left, Bottom-center, Bottom-right, Center)
- **Auto-dismiss functionality** with configurable duration
- **Action buttons** for user interaction
- **Rich content support** (title, message, icons, progress indicators)
- **Smooth animations** with stacking behavior
- **Pause on hover** functionality
- **Accessibility support** with screen reader announcements

## Basic Usage

### Simple Toast Notifications

```cpp
#include "FluentQt/Components/FluentToast.h"
#include "FluentQt/Components/FluentToastManager.h"

// Using the global toast manager (recommended)
FluentToastGlobal::showSuccess("Success", "Operation completed successfully!");
FluentToastGlobal::showError("Error", "Something went wrong.");
FluentToastGlobal::showWarning("Warning", "Please check your input.");
FluentToastGlobal::showInfo("Info", "New update available.");

// Using custom icons
QIcon customIcon(":/icons/custom.png");
FluentToastGlobal::showCustom(customIcon, "Custom", "Custom notification message.");
```

### Creating Toast Instances

```cpp
// Create individual toast instances
auto* toast = FluentToast::createSuccess("Task Complete", "Your file has been saved successfully.");
toast->show();

// Create with custom configuration
FluentToastConfig config;
config.type = FluentToastType::Info;
config.position = FluentToastPosition::TopCenter;
config.duration = 3000; // 3 seconds
config.closable = true;
config.showProgress = true;

auto* customToast = new FluentToast(config);
customToast->setTitle("Custom Toast");
customToast->setMessage("This is a customized toast notification.");
customToast->show();
```

## Toast Types and Styling

### Built-in Toast Types

```cpp
// Success toast (green theme)
auto* successToast = FluentToast::createSuccess(
    "Upload Complete", 
    "Your file has been uploaded successfully."
);

// Error toast (red theme)
auto* errorToast = FluentToast::createError(
    "Connection Failed", 
    "Unable to connect to the server. Please try again."
);

// Warning toast (orange theme)
auto* warningToast = FluentToast::createWarning(
    "Storage Almost Full", 
    "You have less than 10% storage space remaining."
);

// Info toast (blue theme)
auto* infoToast = FluentToast::createInfo(
    "New Feature Available", 
    "Check out the new timeline component in the latest update."
);

// Custom toast with custom styling
FluentToastConfig customConfig;
customConfig.type = FluentToastType::Custom;
customConfig.customBackgroundColor = QColor("#6A4C93");
customConfig.customTextColor = QColor("#FFFFFF");
customConfig.customIcon = QIcon(":/icons/star.png");

auto* customToast = new FluentToast(customConfig);
customToast->setTitle("Premium Feature");
customToast->setMessage("You've unlocked a premium feature!");
```

## Positioning and Layout

### Toast Positioning

```cpp
// Using FluentToastManager for positioned toasts
auto& manager = FluentToastManager::instance();

// Top positions
manager.showInfoAt(FluentToastPosition::TopLeft, "Top Left", "Message");
manager.showInfoAt(FluentToastPosition::TopCenter, "Top Center", "Message");
manager.showInfoAt(FluentToastPosition::TopRight, "Top Right", "Message");

// Bottom positions
manager.showInfoAt(FluentToastPosition::BottomLeft, "Bottom Left", "Message");
manager.showInfoAt(FluentToastPosition::BottomCenter, "Bottom Center", "Message");
manager.showInfoAt(FluentToastPosition::BottomRight, "Bottom Right", "Message");

// Center position
manager.showInfoAt(FluentToastPosition::Center, "Center", "Important message");
```

### Toast Manager Configuration

```cpp
// Configure the global toast manager
FluentToastManagerConfig managerConfig;
managerConfig.defaultPosition = FluentToastPosition::TopRight;
managerConfig.maxVisible = 5;           // Maximum visible toasts
managerConfig.maxQueued = 20;           // Maximum queued toasts
managerConfig.stackSpacing = 8;         // Spacing between stacked toasts
managerConfig.screenMargin = 16;        // Margin from screen edges
managerConfig.allowDuplicates = false;  // Prevent duplicate toasts
managerConfig.stackToasts = true;       // Stack toasts or replace

auto& manager = FluentToastManager::instance();
manager.setConfiguration(managerConfig);

// Set parent widget for positioning (optional)
manager.setParentWidget(mainWindow);
```

## Interactive Features

### Action Buttons

```cpp
auto* toast = FluentToast::createInfo("File Download", "Download completed successfully.");

// Add action buttons
toast->addAction("Open", [=]() {
    // Open the downloaded file
    QDesktopServices::openUrl(QUrl::fromLocalFile(filePath));
});

toast->addAction("Show in Folder", [=]() {
    // Show file in folder
    QDesktopServices::openUrl(QUrl::fromLocalFile(QFileInfo(filePath).dir().path()));
}, false); // false = not primary button

toast->addAction(QIcon(":/icons/share.png"), "Share", [=]() {
    // Share the file
    shareFile(filePath);
}, true); // true = primary button

toast->show();
```

### Progress Indicators

```cpp
auto* progressToast = FluentToast::createInfo("Uploading File", "Please wait...");
progressToast->setShowProgress(true);
progressToast->setProgressRange(0, 100);
progressToast->setPersistent(true); // Don't auto-dismiss

// Update progress
QTimer* progressTimer = new QTimer();
int progress = 0;
connect(progressTimer, &QTimer::timeout, [=]() mutable {
    progress += 10;
    progressToast->setProgress(progress);
    
    if (progress >= 100) {
        progressTimer->stop();
        progressToast->setTitle("Upload Complete");
        progressToast->setMessage("File uploaded successfully!");
        progressToast->setPersistent(false);
        progressToast->setDuration(3000);
        progressTimer->deleteLater();
    }
});
progressTimer->start(200);

progressToast->show();
```

### Pause on Hover

```cpp
auto* toast = FluentToast::createWarning("System Update", "A system update is available.");
toast->setPauseOnHover(true); // Pause auto-dismiss timer on hover
toast->setDuration(5000);

// Handle timer events
connect(toast, &FluentToast::timerPaused, [=]() {
    qDebug() << "Toast timer paused";
});

connect(toast, &FluentToast::timerResumed, [=]() {
    qDebug() << "Toast timer resumed";
});

toast->show();
```

## Advanced Configuration

### Custom Animations

```cpp
FluentToastConfig config;
config.animation = FluentToastAnimation::Slide;  // Slide in/out
config.animationDuration = 300;
config.easingCurve = QEasingCurve::OutCubic;

auto* toast = new FluentToast(config);
toast->setTitle("Animated Toast");
toast->setMessage("This toast slides in smoothly.");
toast->show();
```

### Persistent Toasts

```cpp
// Create a persistent toast that requires manual dismissal
auto* persistentToast = FluentToast::createError(
    "Critical Error", 
    "A critical error occurred. Please contact support."
);
persistentToast->setPersistent(true);  // Won't auto-dismiss
persistentToast->setClosable(true);    // Show close button

// Add action to handle the error
persistentToast->addAction("Contact Support", [=]() {
    QDesktopServices::openUrl(QUrl("mailto:<EMAIL>"));
});

persistentToast->show();
```

### Custom Content and Styling

```cpp
FluentToastConfig config;
config.type = FluentToastType::Custom;
config.autoCalculateColors = false;
config.customBackgroundColor = QColor("#2D3748");
config.customTextColor = QColor("#E2E8F0");
config.customBorderColor = QColor("#4A5568");
config.maxWidth = 500;

auto* customToast = new FluentToast(config);
customToast->setTitle("Custom Styled Toast");
customToast->setMessage("This toast has custom colors and styling.");

// Set custom icon
QIcon customIcon = QIcon(":/icons/notification.png");
customToast->setIcon(customIcon);

customToast->show();
```

## Event Handling

### Toast Events

```cpp
auto* toast = FluentToast::createSuccess("Operation Complete", "Task finished successfully.");

// Handle toast events
connect(toast, &FluentToast::aboutToShow, [=]() {
    qDebug() << "Toast about to show";
});

connect(toast, &FluentToast::shown, [=]() {
    qDebug() << "Toast shown";
});

connect(toast, &FluentToast::aboutToHide, [=]() {
    qDebug() << "Toast about to hide";
});

connect(toast, &FluentToast::hidden, [=]() {
    qDebug() << "Toast hidden";
});

connect(toast, &FluentToast::dismissed, [=]() {
    qDebug() << "Toast dismissed by user";
});

connect(toast, &FluentToast::clicked, [=]() {
    qDebug() << "Toast clicked";
    // Handle toast click
});

connect(toast, &FluentToast::actionTriggered, [=](const QString& actionText) {
    qDebug() << "Action triggered:" << actionText;
});

toast->show();
```

### Manager Events

```cpp
auto& manager = FluentToastManager::instance();

// Handle manager events
connect(&manager, &FluentToastManager::toastShown, [=](FluentToast* toast) {
    qDebug() << "Toast shown:" << toast->title();
});

connect(&manager, &FluentToastManager::toastHidden, [=](FluentToast* toast) {
    qDebug() << "Toast hidden:" << toast->title();
});

connect(&manager, &FluentToastManager::allToastsHidden, [=]() {
    qDebug() << "All toasts hidden";
});
```

## Queue Management

### Controlling Toast Queue

```cpp
auto& manager = FluentToastManager::instance();

// Show multiple toasts (they will be queued if max visible is exceeded)
for (int i = 0; i < 10; ++i) {
    manager.showInfo(QString("Toast %1").arg(i), "This is a queued toast.");
}

// Pause queue processing
manager.pauseQueue();

// Resume queue processing
manager.resumeQueue();

// Clear all queued toasts
manager.clearQueue();

// Hide all visible toasts
manager.hideAll();

// Get queue information
int visibleCount = manager.visibleCount();
int queuedCount = manager.queuedCount();
qDebug() << "Visible:" << visibleCount << "Queued:" << queuedCount;
```

## Accessibility

### Screen Reader Support

```cpp
auto* toast = FluentToast::createInfo("Accessibility Example", "This toast is accessible.");

// Accessibility is automatically configured, but can be customized
toast->setAccessibleName("Information notification: Accessibility Example");
toast->setAccessibleDescription("Toast notification with accessibility support");

// The toast will automatically announce to screen readers when shown
toast->show();
```

## Integration Examples

### With File Operations

```cpp
class FileManager : public QObject {
public:
    void saveFile(const QString& fileName, const QByteArray& data) {
        // Show progress toast
        auto* progressToast = FluentToast::createInfo("Saving File", "Saving " + fileName + "...");
        progressToast->setShowProgress(true);
        progressToast->setPersistent(true);
        progressToast->show();
        
        // Simulate file saving with progress updates
        auto* timer = new QTimer(this);
        int progress = 0;
        connect(timer, &QTimer::timeout, [=]() mutable {
            progress += 20;
            progressToast->setProgress(progress);
            
            if (progress >= 100) {
                timer->stop();
                progressToast->hide();
                
                // Show completion toast
                auto* successToast = FluentToast::createSuccess("File Saved", fileName + " saved successfully.");
                successToast->addAction("Open", [=]() {
                    QDesktopServices::openUrl(QUrl::fromLocalFile(fileName));
                });
                successToast->show();
                
                timer->deleteLater();
            }
        });
        timer->start(500);
    }
};
```

### With Network Operations

```cpp
class NetworkManager : public QObject {
public:
    void downloadFile(const QUrl& url) {
        auto* downloadToast = FluentToast::createInfo("Download Started", "Downloading file...");
        downloadToast->setShowProgress(true);
        downloadToast->setPersistent(true);
        
        downloadToast->addAction("Cancel", [=]() {
            // Cancel download
            cancelDownload();
            downloadToast->hide();
        });
        
        downloadToast->show();
        
        // Connect to download progress
        connect(this, &NetworkManager::downloadProgress, [=](int percent) {
            downloadToast->setProgress(percent);
        });
        
        connect(this, &NetworkManager::downloadFinished, [=](bool success) {
            downloadToast->hide();
            
            if (success) {
                FluentToastGlobal::showSuccess("Download Complete", "File downloaded successfully.");
            } else {
                auto* errorToast = FluentToast::createError("Download Failed", "Failed to download file.");
                errorToast->addAction("Retry", [=]() {
                    downloadFile(url);
                });
                errorToast->show();
            }
        });
        
        startDownload(url);
    }
    
signals:
    void downloadProgress(int percent);
    void downloadFinished(bool success);
    
private:
    void startDownload(const QUrl& url) { /* Implementation */ }
    void cancelDownload() { /* Implementation */ }
};
```

## Best Practices

1. **Use appropriate toast types** for different message categories (Success for confirmations, Error for failures, etc.)

2. **Keep messages concise** - toasts should be quickly readable

3. **Provide actions when appropriate** - allow users to act on the notification

4. **Use progress indicators** for long-running operations

5. **Don't overuse toasts** - too many notifications can be overwhelming

6. **Consider persistence** for critical messages that require user attention

7. **Test with screen readers** to ensure accessibility

8. **Use consistent positioning** throughout your application

9. **Handle queue overflow** gracefully with appropriate limits

10. **Provide clear action labels** that describe what will happen

## API Reference

For complete API documentation, see the header files:
- `FluentQt/Components/FluentToast.h`
- `FluentQt/Components/FluentToastManager.h`

## See Also

- [FluentNotification](FluentNotification.md) - For banner-style notifications
- [FluentDialog](FluentDialog.md) - For modal dialogs
- [FluentProgressBar](FluentProgressBar.md) - For progress indication
- [FluentButton](FluentButton.md) - For action buttons within toasts
